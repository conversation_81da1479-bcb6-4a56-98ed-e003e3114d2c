[{"/Users/<USER>/Documents/dev/goodride/forecaster/src/app/api/health/route.ts": "1", "/Users/<USER>/Documents/dev/goodride/forecaster/src/app/layout.tsx": "2", "/Users/<USER>/Documents/dev/goodride/forecaster/src/app/page.tsx": "3", "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/button.tsx": "4", "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/card.tsx": "5", "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/dialog.tsx": "6", "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/dropdown-menu.tsx": "7", "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/input.tsx": "8", "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/label.tsx": "9", "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/select.tsx": "10", "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/sonner.tsx": "11", "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/switch.tsx": "12", "/Users/<USER>/Documents/dev/goodride/forecaster/src/lib/constants.ts": "13", "/Users/<USER>/Documents/dev/goodride/forecaster/src/lib/format.ts": "14", "/Users/<USER>/Documents/dev/goodride/forecaster/src/lib/utils.ts": "15", "/Users/<USER>/Documents/dev/goodride/forecaster/src/types/index.ts": "16"}, {"size": 716, "mtime": 1748639665767, "results": "17", "hashOfConfig": "18"}, {"size": 1165, "mtime": 1748639562199, "results": "19", "hashOfConfig": "18"}, {"size": 6413, "mtime": 1748639598768, "results": "20", "hashOfConfig": "18"}, {"size": 2123, "mtime": 1748639512824, "results": "21", "hashOfConfig": "18"}, {"size": 1989, "mtime": 1748639512840, "results": "22", "hashOfConfig": "18"}, {"size": 3982, "mtime": 1748639512864, "results": "23", "hashOfConfig": "18"}, {"size": 8284, "mtime": 1748639512873, "results": "24", "hashOfConfig": "18"}, {"size": 967, "mtime": 1748639512843, "results": "25", "hashOfConfig": "18"}, {"size": 611, "mtime": 1748639512845, "results": "26", "hashOfConfig": "18"}, {"size": 6253, "mtime": 1748639512857, "results": "27", "hashOfConfig": "18"}, {"size": 564, "mtime": 1748639512876, "results": "28", "hashOfConfig": "18"}, {"size": 1177, "mtime": 1748639512859, "results": "29", "hashOfConfig": "18"}, {"size": 3870, "mtime": 1748639641198, "results": "30", "hashOfConfig": "18"}, {"size": 4479, "mtime": 1748639659183, "results": "31", "hashOfConfig": "18"}, {"size": 166, "mtime": 1748639458573, "results": "32", "hashOfConfig": "18"}, {"size": 3228, "mtime": 1748639622731, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "la5nqg", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/dev/goodride/forecaster/src/app/api/health/route.ts", ["82"], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/app/page.tsx", ["83", "84"], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/button.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/card.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/dialog.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/input.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/label.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/select.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/sonner.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/components/ui/switch.tsx", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/lib/constants.ts", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/lib/format.ts", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/dev/goodride/forecaster/src/types/index.ts", [], [], {"ruleId": "85", "severity": 2, "message": "86", "line": 17, "column": 12, "nodeType": null, "messageId": "87", "endLine": 17, "endColumn": 17}, {"ruleId": "85", "severity": 2, "message": "88", "line": 9, "column": 10, "nodeType": null, "messageId": "87", "endLine": 9, "endColumn": 17}, {"ruleId": "85", "severity": 2, "message": "89", "line": 9, "column": 19, "nodeType": null, "messageId": "87", "endLine": 9, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'error' is defined but never used.", "unusedVar", "'gpxFile' is assigned a value but never used.", "'setGpxFile' is assigned a value but never used."]